# Quick Auth Fix - Cookie Session Issue

## Problem
- Sign-in succeeds but `/get-session` returns null
- `/v1/listings` returns 401 after successful sign-in
- Better Auth cookies not being properly read by session validation

## Root Cause
Cookie configuration mismatch between sign-in and session validation.

## Fixes Applied

### 1. Better Auth Configuration (`src/lib/auth.ts`)
```typescript
// Added proper cookie configuration
advanced: {
  cookiePrefix: "rendyr",
  crossSubDomainCookies: {
    enabled: true,
    domain: process.env.NODE_ENV === 'development' ? 'localhost' : undefined
  }
},
session: {
  cookieCache: { enabled: true, maxAge: 5 * 60 },
  expiresIn: 60 * 60 * 24 * 7, // 7 days
  updateAge: 60 * 60 * 24, // refresh daily
}
```

### 2. Enhanced Session Endpoint (`src/app.ts`)
- Added debug logging for development
- Improved session validation with proper request context
- Better error handling

## Test the Fix

### 1. Restart Server
```bash
npm run dev
```

### 2. Test Sign-in Flow
```bash
# Sign in
curl -X POST http://localhost:9999/v1/auth/sign-in/email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' \
  --cookie-jar cookies.txt

# Check session (should return user data)
curl -X GET http://localhost:9999/get-session \
  --cookie cookies.txt

# Test protected route (should work)
curl -X GET http://localhost:9999/v1/listings \
  --cookie cookies.txt
```

### 3. Check Debug Logs
Look for these in server logs:
- 🍪 Cookie headers
- 🔐 Session validation results
- 🔍 /get-session debug info

## Expected Results
- `/get-session` returns `{ session: {...}, user: {...} }` after sign-in
- `/v1/listings` returns data (not 401)
- Debug logs show cookies being properly read

## If Still Not Working
1. Check browser DevTools → Application → Cookies
2. Look for `rendyr.session_token` cookie
3. Verify cookie domain/path settings
4. Check CORS origins match exactly
